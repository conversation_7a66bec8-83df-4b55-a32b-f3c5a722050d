"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const index_1 = __importDefault(require("../index"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const request = (0, supertest_1.default)(index_1.default);
describe('API Endpoints', () => {
    // Test the API home endpoint
    describe('Test /api endpoint', () => {
        it('should return welcome message', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/api');
            expect(res.status).toBe(200);
            expect(res.text).toContain('Welcome to the Image API');
        }));
    });
    // Test the image resize endpoint
    describe('Test /images/resize endpoint', () => {
        const testImageDir = path_1.default.resolve('images');
        const testImagePath = path_1.default.join(testImageDir, 'sample.jpg');
        const testAssetsDir = path_1.default.resolve('tests/assets');
        const testAssetPath = path_1.default.join(testAssetsDir, 'test.jpg');
        beforeAll(() => {
            // Ensure test directories exist
            if (!fs_1.default.existsSync(testImageDir)) {
                fs_1.default.mkdirSync(testImageDir, { recursive: true });
            }
            // Copy test image from assets if it doesn't exist
            if (!fs_1.default.existsSync(testImagePath) && fs_1.default.existsSync(testAssetPath)) {
                fs_1.default.copyFileSync(testAssetPath, testImagePath);
            }
            // Verify test image exists
            if (!fs_1.default.existsSync(testImagePath)) {
                throw new Error(`Test image ${testImagePath} not found. Please ensure tests/assets/test.jpg exists.`);
            }
        });
        it('should return status 200 for a valid query', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/images/resize?filename=sample.jpg&width=200&height=200');
            expect(res.status).toBe(200);
            expect(res.headers['content-type']).toMatch(/image/);
        }));
        it('should return status 400 for missing query parameters', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/images/resize?filename=sample.jpg');
            expect(res.status).toBe(400);
        }));
        it('should return status 400 for invalid dimensions', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/images/resize?filename=sample.jpg&width=abc&height=200');
            expect(res.status).toBe(400);
        }));
        it('should return status 404 for non-existent images', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/images/resize?filename=nonexistent.jpg&width=200&height=200');
            expect(res.status).toBe(404);
        }));
        it('should return status 400 for missing filename parameter', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/images/resize?width=200&height=200');
            expect(res.status).toBe(400);
        }));
        it('should return status 400 for negative dimensions', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/images/resize?filename=sample.jpg&width=-100&height=200');
            expect(res.status).toBe(400);
        }));
    });
});
