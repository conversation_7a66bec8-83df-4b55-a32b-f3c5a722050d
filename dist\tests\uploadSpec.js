"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const index_1 = __importDefault(require("../index"));
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const request = (0, supertest_1.default)(index_1.default);
describe('Upload API Endpoints', () => {
    const testAssetsDir = path_1.default.resolve('tests/assets');
    const testImagePath = path_1.default.join(testAssetsDir, 'test.jpg');
    beforeAll(() => {
        // Ensure test assets directory exists
        if (!fs_1.default.existsSync(testAssetsDir)) {
            fs_1.default.mkdirSync(testAssetsDir, { recursive: true });
        }
        // Verify test image exists
        if (!fs_1.default.existsSync(testImagePath)) {
            throw new Error(`Test image ${testImagePath} not found. Please ensure tests/assets/test.jpg exists.`);
        }
    });
    describe('POST /api/upload', () => {
        it('should upload JPG image successfully', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request
                .post('/api/upload')
                .attach('image', testImagePath);
            expect(res.status).toBe(200);
            expect(res.body.success).toBe(true);
            expect(res.body.message).toBe('Image uploaded successfully');
            expect(res.body.file).toBeDefined();
            expect(res.body.file.filename).toBeDefined();
            expect(res.body.file.originalname).toBeDefined();
            expect(res.body.file.size).toBeGreaterThan(0);
            expect(res.body.file.path).toBeDefined();
        }));
        it('should reject request with no file', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.post('/api/upload');
            expect(res.status).toBe(400);
            expect(res.body.success).toBe(false);
            expect(res.body.message).toBe('No file uploaded');
        }));
        it('should reject unsupported file type', () => __awaiter(void 0, void 0, void 0, function* () {
            // Create a temporary text file with PNG extension
            const tempPngPath = path_1.default.join(testAssetsDir, 'temp-test.png');
            fs_1.default.writeFileSync(tempPngPath, 'This is not an image file');
            const res = yield request
                .post('/api/upload')
                .attach('image', tempPngPath);
            expect(res.status).toBe(400);
            expect(res.body.success).toBe(false);
            // Clean up
            if (fs_1.default.existsSync(tempPngPath)) {
                fs_1.default.unlinkSync(tempPngPath);
            }
        }));
        it('should handle large file rejection', () => __awaiter(void 0, void 0, void 0, function* () {
            // Create a large dummy file (over 5MB)
            const largePath = path_1.default.join(testAssetsDir, 'large-test.jpg');
            const largeBuffer = Buffer.alloc(6 * 1024 * 1024, 'a'); // 6MB
            fs_1.default.writeFileSync(largePath, largeBuffer);
            const res = yield request.post('/api/upload').attach('image', largePath);
            expect(res.status).toBe(400);
            expect(res.body.success).toBe(false);
            // Clean up
            if (fs_1.default.existsSync(largePath)) {
                fs_1.default.unlinkSync(largePath);
            }
        }));
    });
    describe('GET /api/images', () => {
        it('should return list of available images', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/api/images');
            expect(res.status).toBe(200);
            expect(res.body.success).toBe(true);
            expect(res.body.count).toBeDefined();
            expect(res.body.images).toBeDefined();
            expect(Array.isArray(res.body.images)).toBe(true);
        }));
        it('should return images with correct structure', () => __awaiter(void 0, void 0, void 0, function* () {
            const res = yield request.get('/api/images');
            expect(res.status).toBe(200);
            if (res.body.images.length > 0) {
                const firstImage = res.body.images[0];
                expect(firstImage.filename).toBeDefined();
                expect(firstImage.path).toBeDefined();
                expect(firstImage.url).toBeDefined();
                expect(firstImage.path).toMatch(/^\/images\//);
                expect(firstImage.url).toMatch(/^\/images\/resize\?filename=.*&width=300&height=200$/);
            }
        }));
    });
});
