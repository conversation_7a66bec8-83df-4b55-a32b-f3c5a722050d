"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const uploadMiddleware_1 = require("../middleware/uploadMiddleware");
const uploader_1 = require("../controllers/uploader");
const router = express_1.default.Router();
router.post('/upload', uploadMiddleware_1.upload, uploader_1.uploadImage);
router.get('/images', uploader_1.getImage);
exports.default = router;
