{"name": "image-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/index.js", "build": "npm run clean && tsc", "dev": "nodemon src/index.ts", "test": "npm run test:setup && jasmine", "test:setup": "node scripts/createTestImage.js", "test:only": "jasmine", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "check": "npm run lint && npm run format:check", "fix": "npm run lint:fix && npm run format", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "type": "commonjs", "dependencies": {"express": "^5.1.0", "multer": "^1.4.5-lts.2", "sharp": "^0.34.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/express": "^5.0.2", "@types/jasmine": "^5.1.8", "@types/multer": "^1.4.12", "@types/node": "^22.15.24", "@types/sharp": "^0.31.1", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-css-modules": "^2.12.0", "eslint-plugin-html": "^8.1.3", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.1.0", "jasmine": "^5.7.1", "jasmine-spec-reporter": "^7.0.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "rimraf": "^6.0.1", "supertest": "^7.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0"}, "jasmine": {"spec_dir": "src", "spec_files": ["**/*Spec.ts", "**/*spec.ts"]}}