# 🖼️ Image Processing API with Node.js and TypeScript

## 📌 Project Overview

This project simulates a real-world scenario where image processing operations are handled on the server side using a **Node.js Express server**, with **disk-based storage** instead of a database. It aims to help developers practice building scalable and testable backend services using TypeScript and common Node.js middleware and utilities.

The app allows users to:
- Resize images dynamically via API.
- Serve resized images from a local cache.
- Upload new images from a frontend interface.
- Quickly generate placeholder images for rapid prototyping.

---

## 🚀 What You Will Build

This project consists of a **frontend single-page app** and a **backend RESTful API**.

### ✅ Backend Features
- **Image Resizing API**: Resize existing images using `width` and `height` query parameters.
- **Image Caching**: Save resized images and serve from disk if already generated.
- **Image Upload**: Accept and store new images uploaded by the user.
- **File System Handling**: Read/write images directly to/from disk (no database).
- **Robust Typing**: Written entirely in TypeScript for better maintainability and type safety.
- **Unit Testing**: Every route and function is unit tested using Jasmine.
- **Code Linting & Formatting**: Prettier + ESLint integration.

### ✅ Frontend Features
- Select an image from a local list.
- Input desired dimensions (width & height).
- Get a direct image URL from the backend API.
- Upload new images via an HTML form and display them.

---

## 🛠️ Installation and Setup

### Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher)

### Installation Steps

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create required directories (if they don't exist):
   ```bash
   mkdir -p images cache
   ```

3. Build the project:
   ```bash
   npm run build
   ```

4. Start the server:
   ```bash
   npm start
   ```

The application will be available at `http://localhost:3000`.

### Development Mode
To run the application in development mode with hot reloading:
```bash
npm run dev
```

## 🧪 Testing

This project uses Jasmine for unit testing. To run the tests:

```bash
npm test
```

The tests cover:
- API endpoints functionality
- Image processing functions
- Error handling

## 📁 Project Structure

```bash
project-root/
├── cache/                 # Cached resized images
├── dist/                  # Transpiled JS output
│   ├── controllers/       # Compiled controller logic
│   ├── routes/            # Compiled route definitions
│   └── tests/             # Compiled test files
├── images/                # Original full-sized images
├── public/                # Frontend static files (HTML/CSS/JS)
├── spec/                  # Jasmine test setup
│   └── support/           # Jasmine config files
├── src/                   # Source code (TypeScript)
│   ├── controllers/       # Business logic for image handling
│   ├── middleware/        # Custom Express middleware
│   ├── routes/            # API route handlers
│   ├── tests/             # Unit test files
│   └── utils/             # Helper functions (e.g. file checks, image logic)
├── .eslintrc              # ESLint configuration
├── .prettierrc            # Prettier configuration
├── package.json           # NPM dependencies and scripts
├── tsconfig.json          # TypeScript configuration
└── README.md              # Project documentation
```

## 📚 API Documentation

### 1. Image Resizing API Endpoint

#### Endpoint

`GET /images/resize`

#### Query Parameters

| Parameter | Type   | Description                                | Required |
| --------- | ------ | ------------------------------------------ | -------- |
| filename  | string | The name of the original image located in the `images` folder (e.g., `sample.jpg`) | Yes       |
| width     | number | The desired width of the resized image               | Yes       |
| height    | number | The desired height of the resized image               | Yes       |

#### Description

This endpoint uses the [Sharp](https://www.npmjs.com/package/sharp) library to process images on the server side.
It resizes the original image to the specified dimensions and converts it to JPEG format, then saves the processed image in the `cache` folder.
If the resized image already exists in the cache, it will be served directly from the cache.

#### Example Request

```
GET /images/resize?filename=sample.jpg&width=300&height=200
```

#### Response

Returns the resized JPEG image file with the requested dimensions (e.g., 300x200). The image is either generated on the fly or served from the cache.

#### Error Responses

- `400 Bad Request`: Missing or invalid parameters
- `404 Not Found`: Image not found in the images directory
- `500 Internal Server Error`: Error processing the image

### 2. Image Upload API Endpoint

#### Endpoint

`POST /api/upload`

#### Request

- Content-Type: `multipart/form-data`
- Form field: `image` (file)
- Accepted formats: JPG/JPEG only

#### Example Response

```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "file": {
    "filename": "uploaded-image-1234567890.jpg",
    "originalname": "my-image.jpg",
    "size": 123456,
    "path": "C:\\path\\to\\images\\uploaded-image-1234567890.jpg"
  }
}
```

#### Features

- **File Validation**: Only JPG/JPEG files are accepted
- **Size Limit**: Maximum file size is 5MB
- **Unique Filenames**: Prevents overwriting existing files
- **Error Handling**: Provides clear error messages for invalid uploads

### 3. List Available Images API Endpoint

#### Endpoint

`GET /api/images`

#### Response

```json
{
  "success": true,
  "count": 3,
  "images": [
    {
      "filename": "sample.jpg",
      "path": "/images/sample.jpg",
      "url": "/images/resize?filename=sample.jpg&width=300&height=200"
    },
    {
      "filename": "landscape.jpg",
      "path": "/images/landscape.jpg",
      "url": "/images/resize?filename=landscape.jpg&width=300&height=200"
    },
    {
      "filename": "portrait.jpg",
      "path": "/images/portrait.jpg",
      "url": "/images/resize?filename=portrait.jpg&width=300&height=200"
    }
  ]
}
```

## 🔄 Caching System

This API implements an efficient caching system to improve performance:

- When an image is resized for the first time, the result is stored in a `cache` directory.
- Subsequent requests for the same image with the same dimensions are served directly from the cache.
- Each cached image has a unique filename based on the original image name and the requested dimensions (e.g., `sample-300x200.jpg`).

Benefits:
- Significantly faster response times for repeated requests
- Reduced server load and processing power usage
- Lower bandwidth consumption

The cache is stored on disk in the `cache` directory, making it persistent across server restarts.

## 🖥️ Web Interface

A simple web interface is available at the root URL (`/`) that allows you to:
- Upload new images through a form
- View all available images in a gallery
- Resize images with custom dimensions
- Get direct URLs to resized images

### Interface Features
- Responsive design that works on mobile and desktop
- Drag and drop file upload
- Image preview before upload
- Real-time feedback on operations
- Copy-to-clipboard functionality for image URLs

## 🧰 Technologies Used

- **Backend**:
  - Node.js & Express.js: Server framework
  - TypeScript: Type-safe JavaScript
  - Sharp: Image processing library
  - Multer: File upload handling
  - Jasmine: Testing framework
  - Supertest: HTTP testing

- **Frontend**:
  - HTML5, CSS3, JavaScript
  - Font Awesome: Icons
  - Google Fonts: Typography

## 🔧 Development Scripts

- `npm start`: Start the production server
- `npm run build`: Build the TypeScript code
- `npm run dev`: Start the development server with hot reloading
- `npm test`: Run the test suite
- `npm run lint`: Run ESLint to check code quality
- `npm run lint:fix`: Run ESLint and automatically fix issues
- `npm run format`: Format code with Prettier
- `npm run format:check`: Check if code is properly formatted
- `npm run check`: Run both linting and format checking
- `npm run fix`: Run both linting with auto-fix and formatting
- `npm run clean`: Clean the build directory

## ❓ Troubleshooting

### Common Issues

1. **Images not showing up in the gallery**
   - Make sure the `images` directory exists and has proper permissions
   - Verify that the images are in JPG/JPEG format

2. **Resizing fails**
   - Check that the Sharp library is properly installed
   - Ensure the `cache` directory exists and has write permissions
   - Verify that the width and height parameters are valid positive numbers

3. **Upload fails**
   - Ensure the file is a valid JPG/JPEG image
   - Check that the file size is under 5MB
   - Verify that the `images` directory has write permissions

## 📝 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 👨‍💻 Author

Eng. Yousef Sherif

---

**Note**: This project is designed for educational purposes and demonstrates best practices for building a Node.js API with TypeScript.