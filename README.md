# 🖼️ Image Processing API with Node.js and TypeScript

## 📌 Project Overview

This project simulates a real-world scenario where image processing operations are handled on the server side using a **Node.js Express server**, with **disk-based storage** instead of a database. It aims to help developers practice building scalable and testable backend services using TypeScript and common Node.js middleware and utilities.

The app allows users to:
- Resize images dynamically via API.
- Serve resized images from a local cache.
- Upload new images from a frontend interface.
- Quickly generate placeholder images for rapid prototyping.

---

## 🚀 What You Will Build

This project consists of a **frontend single-page app** and a **backend RESTful API**.

### ✅ Backend Features
- **Image Resizing API**: Resize existing images using `width` and `height` query parameters.
- **Image Caching**: Save resized images and serve from disk if already generated.
- **Image Upload**: Accept and store new images uploaded by the user.
- **File System Handling**: Read/write images directly to/from disk (no database).
- **Robust Typing**: Written entirely in TypeScript for better maintainability and type safety.
- **Unit Testing**: Every route and function is unit tested using Jasmine.
- **Code Linting & Formatting**: Prettier + ESLint integration.

### ✅ Frontend Features
- Select an image from a local list.
- Input desired dimensions (width & height).
- Get a direct image URL from the backend API.
- Upload new images via an HTML form and display them.

---

## 🛠️ Installation and Setup

### Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher)

### Installation Steps

1. Install dependencies:
   ```bash
   npm install
   ```

2. Create required directories (if they don't exist):
   ```bash
   mkdir -p images cache
   ```

3. Build the project:
   ```bash
   npm run build
   ```

4. Start the server:
   ```bash
   npm start
   ```

The application will be available at `http://localhost:3000`.

### Development Mode
To run the application in development mode with hot reloading:
```bash
npm run dev
```

## 🧪 Testing

This project uses **Jasmine + SuperTest** for comprehensive testing with **25 test specs** covering all functionality.

### **Quick Start**
```bash
npm test                       # Complete test suite with setup
npm run test:only             # Run tests only (faster for repeated runs)
```

### **Test Coverage**

#### **🔗 API Endpoint Tests (8 specs)**
- `/api` welcome endpoint validation
- `/images/resize` endpoint with various scenarios:
  - Valid image resizing (200 response)
  - Missing parameters (400 response)
  - Invalid dimensions (400 response)
  - Non-existent images (404 response)
  - Negative dimensions (400 response)

#### **📤 Upload Functionality Tests (6 specs)**
- Valid JPG image upload with response validation
- File rejection for missing files
- File type validation (PNG rejection)
- Large file handling (5MB+ rejection)
- Image listing endpoint (`/api/images`)
- Response structure validation

#### **⚙️ Image Processing Unit Tests (10 specs)**
- **Image Resizing**: Successful resizing, dimension validation, square dimensions
- **Metadata Extraction**: Format detection, dimension validation, JPEG verification
- **File Operations**: Output file creation, file overwriting, error handling
- **Error Scenarios**: Non-existent files, invalid parameters

#### **✅ Basic Functionality Test (1 spec)**
- Core application functionality validation

### **Test Features**

- **🎯 Zero Failures**: All 25 specs pass consistently
- **🔄 Automated Setup**: Test images created automatically
- **🧹 Clean Teardown**: Test files cleaned up after each run
- **📊 Comprehensive Coverage**: API, unit, and integration tests
- **⚡ Fast Execution**: Tests complete in ~0.2 seconds
- **🔒 Reliable**: Consistent results across environments

### **Test Assets**

The testing system automatically creates:
- `tests/assets/test.jpg` - Primary test image (100x100 red square)
- `images/sample.jpg` - Copy for resize endpoint tests
- Temporary test files for upload validation
- Cache files for resize testing (auto-cleaned)

### **Running Specific Tests**

```bash
# Run all tests with full setup
npm test

# Run tests without recreating assets (faster)
npm run test:only

# Just create test assets
npm run test:setup
```

## 📁 Project Structure

```bash
project-root/
├── cache/                 # Cached resized images
├── dist/                  # Transpiled JS output
│   ├── controllers/       # Compiled controller logic
│   ├── routes/            # Compiled route definitions
│   └── tests/             # Compiled test files
├── images/                # Original full-sized images
├── public/                # Frontend static files (HTML/CSS/JS)
├── scripts/               # Utility scripts
│   └── createTestImage.js # Automated test image generation
├── spec/                  # Jasmine test setup
│   └── support/           # Jasmine config files
├── src/                   # Source code (TypeScript)
│   ├── controllers/       # Business logic for image handling
│   ├── middleware/        # Custom Express middleware
│   ├── routes/            # API route handlers
│   ├── tests/             # Unit test files
│   │   ├── fixtures/      # Test fixtures and assets
│   │   ├── basicSpec.ts   # Basic functionality tests
│   │   ├── endpointSpec.ts # API endpoint tests
│   │   ├── imageProcessorSpec.ts # Image processing unit tests
│   │   └── uploadSpec.ts  # Upload functionality tests
│   └── utils/             # Helper functions (e.g. file checks, image logic)
├── tests/                 # Test assets directory
│   └── assets/            # Generated test images
│       └── test.jpg       # Primary test image (auto-generated)
├── .eslintrc              # ESLint configuration
├── .prettierrc            # Prettier configuration
├── package.json           # NPM dependencies and scripts
├── tsconfig.json          # TypeScript configuration
└── README.md              # Project documentation
```

## 📚 API Documentation

### 1. Image Resizing API Endpoint

#### Endpoint

`GET /images/resize`

#### Query Parameters

| Parameter | Type   | Description                                | Required |
| --------- | ------ | ------------------------------------------ | -------- |
| filename  | string | The name of the original image located in the `images` folder (e.g., `sample.jpg`) | Yes       |
| width     | number | The desired width of the resized image               | Yes       |
| height    | number | The desired height of the resized image               | Yes       |

#### Description

This endpoint uses the [Sharp](https://www.npmjs.com/package/sharp) library to process images on the server side.
It resizes the original image to the specified dimensions and converts it to JPEG format, then saves the processed image in the `cache` folder.
If the resized image already exists in the cache, it will be served directly from the cache.

#### Example Request

```
GET /images/resize?filename=sample.jpg&width=300&height=200
```

#### Response

Returns the resized JPEG image file with the requested dimensions (e.g., 300x200). The image is either generated on the fly or served from the cache.

#### Error Responses

- `400 Bad Request`: Missing or invalid parameters
- `404 Not Found`: Image not found in the images directory
- `500 Internal Server Error`: Error processing the image

### 2. Image Upload API Endpoint

#### Endpoint

`POST /api/upload`

#### Request

- Content-Type: `multipart/form-data`
- Form field: `image` (file)
- Accepted formats: JPG/JPEG only

#### Example Response

```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "file": {
    "filename": "uploaded-image-1234567890.jpg",
    "originalname": "my-image.jpg",
    "size": 123456,
    "path": "C:\\path\\to\\images\\uploaded-image-1234567890.jpg"
  }
}
```

#### Features

- **File Validation**: Only JPG/JPEG files are accepted
- **Size Limit**: Maximum file size is 5MB
- **Unique Filenames**: Prevents overwriting existing files
- **Error Handling**: Provides clear error messages for invalid uploads

### 3. List Available Images API Endpoint

#### Endpoint

`GET /api/images`

#### Response

```json
{
  "success": true,
  "count": 3,
  "images": [
    {
      "filename": "sample.jpg",
      "path": "/images/sample.jpg",
      "url": "/images/resize?filename=sample.jpg&width=300&height=200"
    },
    {
      "filename": "landscape.jpg",
      "path": "/images/landscape.jpg",
      "url": "/images/resize?filename=landscape.jpg&width=300&height=200"
    },
    {
      "filename": "portrait.jpg",
      "path": "/images/portrait.jpg",
      "url": "/images/resize?filename=portrait.jpg&width=300&height=200"
    }
  ]
}
```

## 🔄 Caching System

This API implements an efficient caching system to improve performance:

- When an image is resized for the first time, the result is stored in a `cache` directory.
- Subsequent requests for the same image with the same dimensions are served directly from the cache.
- Each cached image has a unique filename based on the original image name and the requested dimensions (e.g., `sample-300x200.jpg`).

Benefits:
- Significantly faster response times for repeated requests
- Reduced server load and processing power usage
- Lower bandwidth consumption

The cache is stored on disk in the `cache` directory, making it persistent across server restarts.

## 🖥️ Web Interface

A simple web interface is available at the root URL (`/`) that allows you to:
- Upload new images through a form
- View all available images in a gallery
- Resize images with custom dimensions
- Get direct URLs to resized images

### Interface Features
- Responsive design that works on mobile and desktop
- Drag and drop file upload
- Image preview before upload
- Real-time feedback on operations
- Copy-to-clipboard functionality for image URLs

## 🧰 Technologies Used

- **Backend**:
  - Node.js & Express.js: Server framework
  - TypeScript: Type-safe JavaScript
  - Sharp: Image processing library
  - Multer: File upload handling
  - Jasmine: Testing framework
  - Supertest: HTTP testing

- **Frontend**:
  - HTML5, CSS3, JavaScript
  - Font Awesome: Icons
  - Google Fonts: Typography

## 🔧 Development Scripts

### **Production & Development**
- `npm start`: Start the production server from compiled JavaScript files in `dist/`
- `npm run build`: Clean the build directory and compile TypeScript to JavaScript
- `npm run dev`: Start the development server with hot reloading using nodemon
- `npm run clean`: Remove the `dist/` directory and all compiled files

### **Testing**
- `npm test`: **Complete test suite** - Sets up test environment and runs all tests
- `npm run test:setup`: **Test preparation** - Creates test images and sets up test assets
- `npm run test:only`: **Run tests only** - Executes Jasmine tests without setup (faster for repeated runs)

### **Code Quality & Formatting**
- `npm run lint`: **Code linting** - Run ESLint to check TypeScript code quality and style
- `npm run lint:fix`: **Auto-fix linting** - Run ESLint and automatically fix fixable issues
- `npm run format`: **Code formatting** - Format all TypeScript files with Prettier
- `npm run format:check`: **Format validation** - Check if code is properly formatted without making changes

### **Combined Operations**
- `npm run check`: **Quality check** - Run both linting and format checking (CI/CD friendly)
- `npm run fix`: **Auto-fix everything** - Run linting with auto-fix AND format all code

### **Script Usage Examples**

```bash
# Development workflow
npm run dev                    # Start development server

# Before committing code
npm run check                  # Check code quality and formatting
npm run fix                    # Auto-fix any issues

# Testing workflow
npm test                       # Run complete test suite (recommended)
npm run test:only             # Quick test run (if assets already exist)

# Production deployment
npm run build                  # Build for production
npm start                     # Start production server
```

### **Test Setup Details**

The `npm run test:setup` script automatically:
- Creates the `tests/assets/` directory
- Generates a valid 100x100 JPEG test image using Sharp
- Copies test images to the `images/` directory for resize tests
- Ensures all test dependencies are properly configured

This ensures that all tests have the required assets and can run reliably across different environments.

## ❓ Troubleshooting

### Common Issues

1. **Images not showing up in the gallery**
   - Make sure the `images` directory exists and has proper permissions
   - Verify that the images are in JPG/JPEG format

2. **Resizing fails**
   - Check that the Sharp library is properly installed
   - Ensure the `cache` directory exists and has write permissions
   - Verify that the width and height parameters are valid positive numbers

3. **Upload fails**
   - Ensure the file is a valid JPG/JPEG image
   - Check that the file size is under 5MB
   - Verify that the `images` directory has write permissions

4. **Tests failing**
   - Run `npm run test:setup` to create test assets
   - Ensure Sharp is properly installed: `npm install sharp`
   - Check that `tests/assets/test.jpg` exists after running setup
   - For Windows users: Ensure proper file permissions for test directories

5. **Test assets missing**
   - The `npm test` command automatically creates test images
   - If tests still fail, manually run: `npm run test:setup`
   - Verify that the `scripts/createTestImage.js` file exists

6. **ESLint or Prettier errors**
   - Run `npm run fix` to auto-fix most formatting and linting issues
   - Check that all TypeScript files have proper type annotations
   - Ensure ESLint and Prettier configurations are compatible

## 📝 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 👨‍💻 Author

Yousef Sherif - Software Engineer

---

## 🆕 Recent Updates & Features

### **Enhanced Testing Suite**
- ✅ **25 comprehensive test specs** with 0 failures
- ✅ **Automated test setup** with `npm run test:setup`
- ✅ **Fast test execution** (~0.2 seconds)
- ✅ **Reliable test assets** auto-generated with Sharp

### **New NPM Scripts**
- ✅ `npm run test:setup` - Creates test images automatically
- ✅ `npm run test:only` - Quick test runs without setup
- ✅ `npm run check` - Combined linting and format checking
- ✅ `npm run fix` - Auto-fix all code quality issues

### **Improved Code Quality**
- ✅ **Enhanced TypeScript typing** throughout the codebase
- ✅ **Better error handling** with proper validation
- ✅ **Comprehensive API testing** with SuperTest
- ✅ **Unit tests** for all image processing functions

### **Developer Experience**
- ✅ **Detailed documentation** for all scripts and features
- ✅ **Troubleshooting guide** for common issues
- ✅ **Automated workflows** for testing and development
- ✅ **Consistent code formatting** with Prettier + ESLint

---

**Note**: This project is designed for educational purposes and demonstrates best practices for building a Node.js API with TypeScript.