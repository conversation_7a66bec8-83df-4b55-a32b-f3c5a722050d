"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resizeImageHandler = exports.getHome = void 0;
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const processing_1 = require("./processing");
const getHome = (_req, res) => {
    res.status(200).send('Welcome to the Image API!');
};
exports.getHome = getHome;
const resizeImageHandler = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const params = req.query;
        const theImage = {
            filename: params.filename,
            width: parseInt(params.width),
            height: parseInt(params.height),
        };
        if (isNaN(theImage.width) ||
            isNaN(theImage.height) ||
            theImage.width <= 0 ||
            theImage.height <= 0) {
            res.status(400).send('Width and height must be positive numbers');
            return;
        }
        if (Object.keys(params).length != 0) {
            const filepath = path_1.default.resolve('images/' + theImage.filename);
            const outName = `${path_1.default.parse(theImage.filename).name}-${theImage.width}x${theImage.height}.jpg`;
            const outPath = path_1.default.resolve(`cache/${outName}`);
            if (!fs_1.default.existsSync(filepath)) {
                res.status(404).send(`Image '${theImage.filename}' not found`);
                return;
            }
            if (fs_1.default.existsSync(outPath)) {
                console.log(`Serving cached image: ${outName}`);
                res.sendFile(outPath);
                return;
            }
            yield (0, processing_1.resizeImage)(filepath, outPath, theImage.width, theImage.height);
            console.log(`Image resized and saved: ${outName}`);
            res.sendFile(outPath);
        }
        else {
            res
                .status(400)
                .send('Missing required query parameters: filename, width, and height are required');
        }
    }
    catch (error) {
        console.error('Error processing image:', error);
        const errorM = error instanceof Error ? error.message : 'Unknown error occurred';
        res.status(500).send(`Error processing image: ${errorM}`);
    }
});
exports.resizeImageHandler = resizeImageHandler;
