import { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import { resizeImg } from './processing';

export const getHome = (_req: Request, res: Response): void => {
  res.status(200).send('Welcome to the Image API!');
};

export const resizeImgHandler = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const params = req.query;

    interface imgDetails {
      filename: string;
      width: number;
      height: number;
    }

    const theImg: imgDetails = {
      filename: params.filename as string,
      width: parseInt(params.width as string),
      height: parseInt(params.height as string),
    };

    if (
      isNaN(theImg.width) ||
      isNaN(theImg.height) ||
      theImg.width <= 0 ||
      theImg.height <= 0
    ) {
      res.status(400).send('Width and height must be positive numbers');
      return;
    }

    if (Object.keys(params).length !== 0) {
      const filepath: string = path.resolve('images/' + theImg.filename);
      const outName: string = `${path.parse(theImg.filename).name}-${theImg.width}x${theImg.height}.jpg`;
      const outPath = path.resolve(`cache/${outName}`);

      if (!fs.existsSync(filepath)) {
        res.status(404).send(`Image '${theImg.filename}' not found`);
        return;
      }

      if (fs.existsSync(outPath)) {
        console.log(`Serving cached image: ${outName}`);
        res.sendFile(outPath);
        return;
      }

      await resizeImg(filepath, outPath, theImg.width, theImg.height);
      console.log(`Image resized and saved: ${outName}`);
      res.sendFile(outPath);
    } else {
      res
        .status(400)
        .send(
          'Missing required query parameters: filename, width, and height are required'
        );
    }
  } catch (error: unknown) {
    console.error('Error processing image:', error);
    const errorM =
      error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).send(`Error processing image: ${errorM}`);
  }
};
