"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const processing_1 = require("../controllers/processing");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const sharp_1 = __importDefault(require("sharp"));
// Test whole image's process
describe('Image Processing Functions', () => {
    const testAssetsDir = path_1.default.resolve('tests/assets');
    const testAssetPath = path_1.default.join(testAssetsDir, 'test.jpg');
    const testImageDir = path_1.default.resolve('images');
    const cacheDir = path_1.default.resolve('cache');
    const testImageName = 'sample.jpg';
    const inputPath = path_1.default.join(testImageDir, testImageName);
    const outputPath = path_1.default.join(cacheDir, 'sample_test_200x200.jpg');
    beforeAll(() => {
        // Ensure directories exist
        if (!fs_1.default.existsSync(testImageDir)) {
            fs_1.default.mkdirSync(testImageDir, { recursive: true });
        }
        if (!fs_1.default.existsSync(cacheDir)) {
            fs_1.default.mkdirSync(cacheDir, { recursive: true });
        }
        // Copy test image from assets if it doesn't exist
        if (!fs_1.default.existsSync(inputPath) && fs_1.default.existsSync(testAssetPath)) {
            fs_1.default.copyFileSync(testAssetPath, inputPath);
        }
        // Verify test image exists
        if (!fs_1.default.existsSync(inputPath)) {
            throw new Error(`Test image ${inputPath} not found. Please ensure tests/assets/test.jpg exists.`);
        }
    });
    afterEach(() => {
        // Clean up test output files
        const testOutputs = [
            outputPath,
            path_1.default.join(cacheDir, 'sample_test_300x200.jpg'),
            path_1.default.join(cacheDir, 'sample_test_150x150.jpg'),
        ];
        testOutputs.forEach((filePath) => {
            if (fs_1.default.existsSync(filePath)) {
                fs_1.default.unlinkSync(filePath);
            }
        });
    });
    describe('Image Resizing', () => {
        it('should resize the image successfully', () => __awaiter(void 0, void 0, void 0, function* () {
            yield (0, processing_1.resizeImage)(inputPath, outputPath, 200, 200);
            const exists = fs_1.default.existsSync(outputPath);
            expect(exists).toBeTrue();
        }));
        it('should create an image with the correct dimensions', () => __awaiter(void 0, void 0, void 0, function* () {
            const targetWidth = 300;
            const targetHeight = 200;
            const customOutputPath = path_1.default.join(cacheDir, `sample_test_${targetWidth}x${targetHeight}.jpg`);
            yield (0, processing_1.resizeImage)(inputPath, customOutputPath, targetWidth, targetHeight);
            const metadata = yield (0, sharp_1.default)(customOutputPath).metadata();
            expect(metadata.width).toEqual(targetWidth);
            expect(metadata.height).toEqual(targetHeight);
        }));
        it('should maintain aspect ratio when resizing to square dimensions', () => __awaiter(void 0, void 0, void 0, function* () {
            const targetSize = 150;
            const squareOutputPath = path_1.default.join(cacheDir, `sample_test_${targetSize}x${targetSize}.jpg`);
            yield (0, processing_1.resizeImage)(inputPath, squareOutputPath, targetSize, targetSize);
            const exists = fs_1.default.existsSync(squareOutputPath);
            expect(exists).toBeTrue();
            const metadata = yield (0, sharp_1.default)(squareOutputPath).metadata();
            expect(metadata.width).toEqual(targetSize);
            expect(metadata.height).toEqual(targetSize);
        }));
        it('should throw an error for non-existent images', () => __awaiter(void 0, void 0, void 0, function* () {
            const nonExistentPath = path_1.default.join(testImageDir, 'non-existent.jpg');
            yield expectAsync((0, processing_1.resizeImage)(nonExistentPath, outputPath, 200, 200)).toBeRejected();
        }));
        it('should handle invalid dimensions gracefully', () => __awaiter(void 0, void 0, void 0, function* () {
            yield expectAsync((0, processing_1.resizeImage)(inputPath, outputPath, 0, 200)).toBeRejected();
            yield expectAsync((0, processing_1.resizeImage)(inputPath, outputPath, -100, 200)).toBeRejected();
        }));
    });
    describe('Image Metadata', () => {
        it('should get image metadata correctly', () => __awaiter(void 0, void 0, void 0, function* () {
            const metadata = yield (0, processing_1.getImageInfo)(inputPath);
            expect(metadata).toBeDefined();
            expect(metadata.format).toBeDefined();
            expect(metadata.width).toBeGreaterThan(0);
            expect(metadata.height).toBeGreaterThan(0);
        }));
        it('should return correct format for JPEG images', () => __awaiter(void 0, void 0, void 0, function* () {
            const metadata = yield (0, processing_1.getImageInfo)(inputPath);
            expect(metadata.format).toBe('jpeg');
        }));
        it('should return valid dimensions', () => __awaiter(void 0, void 0, void 0, function* () {
            const metadata = yield (0, processing_1.getImageInfo)(inputPath);
            expect(typeof metadata.width).toBe('number');
            expect(typeof metadata.height).toBe('number');
            expect(metadata.width).toBeGreaterThan(0);
            expect(metadata.height).toBeGreaterThan(0);
        }));
        it('should throw an error for non-existent image metadata', () => __awaiter(void 0, void 0, void 0, function* () {
            const nonExistentPath = path_1.default.join(testImageDir, 'non-existent.jpg');
            yield expectAsync((0, processing_1.getImageInfo)(nonExistentPath)).toBeRejected();
        }));
    });
    describe('File Operations', () => {
        it('should create output file in correct location', () => __awaiter(void 0, void 0, void 0, function* () {
            const testOutputPath = path_1.default.join(cacheDir, 'test-output.jpg');
            yield (0, processing_1.resizeImage)(inputPath, testOutputPath, 100, 100);
            expect(fs_1.default.existsSync(testOutputPath)).toBeTrue();
            // Clean up
            if (fs_1.default.existsSync(testOutputPath)) {
                fs_1.default.unlinkSync(testOutputPath);
            }
        }));
        it('should overwrite existing output file', () => __awaiter(void 0, void 0, void 0, function* () {
            const testOutputPath = path_1.default.join(cacheDir, 'overwrite-test.jpg');
            // Create first version
            yield (0, processing_1.resizeImage)(inputPath, testOutputPath, 100, 100);
            const firstStats = fs_1.default.statSync(testOutputPath);
            // Wait a moment to ensure different timestamps
            yield new Promise((resolve) => setTimeout(resolve, 10));
            // Create second version with different dimensions
            yield (0, processing_1.resizeImage)(inputPath, testOutputPath, 200, 200);
            const secondStats = fs_1.default.statSync(testOutputPath);
            expect(firstStats.mtime.getTime()).toBeLessThan(secondStats.mtime.getTime());
            // Verify new dimensions
            const metadata = yield (0, sharp_1.default)(testOutputPath).metadata();
            expect(metadata.width).toBe(200);
            expect(metadata.height).toBe(200);
            // Clean up
            if (fs_1.default.existsSync(testOutputPath)) {
                fs_1.default.unlinkSync(testOutputPath);
            }
        }));
    });
});
