2025-05-19 v8.1.3

- Fix compatibility with ESLint 9.27.0 #300
- Update dependencies

2024-09-21 v8.1.2

- Fix sourceType config for ESLint 9
- Don't require espree directly #271

2024-04-22 v8.1.1

- Fix compatibility with @eslint/config-inspector #267
- Drop Jest #266
- Update dependencies

2024-04-10 v8.1.0

- Introduce the `html/ignore-tags-without-type` setting #249
- Fix files with unknown extensions being considered as XML #257
- Fix plugin applied even if it's not in the configuration #263
- Update dependencies

2024-02-09 v8.0.0

- **Breaking: drop Node 12 and 14 support**
- ESLint 9 support (flat config)

2022-07-25 v7.1.0

- Add support for @html-eslint and @angular-eslint/template plugins #176

2022-07-18 v7.0.0

- **Breaking: drop Node 10 support**
- Allow temporarily disabling ESLint with HTML comments
- Allow configuring which script tag names should be considered as JavaScript #137

2021-09-20 v6.2.0

- Update dependencies
- Fix support for ESLint v8.0.0-beta.2
- Add .riot extension #146

2021-03-08 v6.1.2

- Update htmlparser2 #141
- Update dependencies

2020-11-11 v6.1.1

- Update dependencies
- Move from travis to github actions

2020-09-06 v6.1.0

- Allow dots in extensions #127

2020-08-08 v6.0.3

- Update dependencies

2020-04-15 v6.0.2

- Remove npm-shrinkwrap.json from the npm package #125

2020-04-05 v6.0.1 (the 5th anniversary release)

- Update dependencies
- Run CI against eslint@next
- Add eslint-plugin-php-markup reference to the README

2019-06-26 v6.0.0 (the 666 release)

- **Breaking: drop Node 6 support**
- Fix support for ESLint v6

2019-05-13 v5.0.5

- Fix support for ESLint v6.0.0-alpha.1 (again)
- Improved integration tests

2019-05-12 v5.0.4

- Fix support for ESLint v6.0.0-alpha.1 #117

2019-02-02 v5.0.3

- Fix support for `parserOptions.ecmaFeatures.globalReturn: true` while sharing scope between multiple script tags

2019-02-02 v5.0.2

- Fix support for the --report-unused-disabled-directives option #111

2019-02-02 v5.0.1

- Fix compatibility with ESLint 5.13.0
- Update dependencies

2018-11-11 v5.0.0

- **Breaking: drop Node 4 support**
- **Breaking: don't lint .vue files by default**
- Update dependencies

2018-09-22 v4.0.6

- Ignore script tags with a `src` attribute #102
- More detailed error reporting

2018-06-20 v4.0.5

- Fix typo regression from v4.0.4

2018-06-20 v4.0.4

- Request detailed informations when ESLint is not found

2018-04-11 v4.0.3

- Prevent patching ESLint multiple times #89

2018-01-24 v4.0.2

- Fix compatibility with tslint #85

2017-11-22 v4.0.1

- Fix processing files after processing a HTML file without script tag #82

2017-11-12 v4.0.0

- **Breaking: drop ESLint < 4.7 support**
- **Breaking: Non-module script tags are sharing the global scope** #66
- Lint script tags with `type="module"` by default

2017-09-16 v3.2.2

- Fix ESLint 4.7 support

2017-09-03 v3.2.1

- Fix ESLint 4.6 support #77

2017-08-12 v3.2.0

- improve compatibility with the `prettier` plugin by ignoring the first empty script line #76
- fix compatibility with the `eslint-comments` plugin #70
- add some _Troubleshooting_ documentation about linting template markup and Vue files

2017-07-18 v3.1.1

- Fix node 4 support #71 #72

2017-07-11 v3.1.0

- Remap messages "source" property to fix custom formatters support #69

2017-06-12 v3.0.0 [migration guide](MIGRATION_TO_V3.md)

- **Breaking: lint script tags separately** #49 #55 #56
- ESLint 4 support #57
- Support nested settings in a "html" object #58

2017-05-06 v2.0.3

- No change, new version to work around a publish issue

2017-05-06 v2.0.2

- Support self-closing script tags in XHTML #52
- Fix tests for ESLint 4.0.0-alpha.{0-2}

2017-02-16 v2.0.1

- Support for empty filenames #48
- Support for empty files #46

2017-01-25 v2.0.0

- **Breaking: drop `html/xml-mode` setting**
- `eslint --fix` support #23
- Allow configuring HTML and XML extensions via ESLint config
- Allow configuring JS MIME types via ESLint config
- Report correct end locations of error messages
- Report correct fix ranges of error messages

2016-11-18 v1.7.0

- Ignore all warnings for non-script lines #37

2016-11-05 v1.6.0

- Patch all loaded eslint packages #28

2016-10-23 v1.5.5

- Fix typo in handlebars extension #36

2016-10-22 v1.5.4

- Support `.nunjucks` files #35

2016-09-24 v1.5.3

- Fix tests for ESLint 3.6.0 #32
- Support `.we` files #30

2016-08-06 v1.5.2

- Laxer way to retrieve the eslint package #27
- Support `.erb` files #26
- Support `.tag` files #25

2016-05-22 v1.5.1

- Republish v1.5.0 on NPM, files were missing because of npm/npm#5082

2016-05-22 v1.5.0

- Support `.handlebars` files
- Introduce the `html/xml-mode` setting and parse XML files as XML #20
- Support `.twig` files #21
- Support the `text/ecmascript-6` mime type #17

2016-02-16 v1.4.0

- Support `.php` files

2016-01-27 v1.3.0

- Fix crlf newlines support #16
- Introduce the `html/report-bad-indent` setting
- Introduce the `html/indent` setting
- Support more standard JS MIME types #15

2016-01-15 v1.2.0

- Support the `text/babel` mime type
- Support `.mustache` files #11

2015-11-02 v1.1.0

- Stop non-JS lines breaking `no-multiple-empty-lines` #8

2015-08-22 v1.0.4

- Check if there is some lines before striping the last line spaces #7

2015-08-17 v1.0.3

- Support `.hbs` files

2015-06-30 v1.0.2

- Support `.vue` files #4

2015-04-18 v1.0.1

- Fix space eating issue
- Introduce unit tests

2015-04-05 v1.0.0

- Add README
- Initial commit
