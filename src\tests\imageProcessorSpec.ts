import { resizeImg, getImgInfo } from '../controllers/processing';
import path from 'path';
import fs from 'fs';
import sharp from 'sharp';

// Test whole image's process
describe('Image Processing Functions', (): void => {
  const testAssetsDir = path.resolve('tests/assets');
  const testAssetPath = path.join(testAssetsDir, 'test.jpg');
  const testImageDir = path.resolve('images');
  const cacheDir = path.resolve('cache');
  const testImageName = 'sample.jpg';
  const inputPath = path.join(testImageDir, testImageName);
  const outputPath = path.join(cacheDir, 'sample_test_200x200.jpg');

  beforeAll((): void => {
    // Ensure directories exist
    if (!fs.existsSync(testImageDir)) {
      fs.mkdirSync(testImageDir, { recursive: true });
    }
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }

    // Copy test image from assets if it doesn't exist
    if (!fs.existsSync(inputPath) && fs.existsSync(testAssetPath)) {
      fs.copyFileSync(testAssetPath, inputPath);
    }

    // Verify test image exists
    if (!fs.existsSync(inputPath)) {
      throw new Error(`Test image ${inputPath} not found. Please ensure tests/assets/test.jpg exists.`);
    }
  });

  afterEach((): void => {
    // Clean up test output files
    const testOutputs = [
      outputPath,
      path.join(cacheDir, 'sample_test_300x200.jpg'),
      path.join(cacheDir, 'sample_test_150x150.jpg'),
    ];

    testOutputs.forEach((filePath) => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    });
  });

  describe('Image Resizing', (): void => {
    it('should resize the image successfully', async (): Promise<void> => {
      await resizeImg(inputPath, outputPath, 200, 200);
      const exists = fs.existsSync(outputPath);
      expect(exists).toBeTrue();
    });

    it('should create an image with the correct dimensions', async (): Promise<void> => {
      const targetWidth = 300;
      const targetHeight = 200;
      const customOutputPath = path.join(
        cacheDir,
        `sample_test_${targetWidth}x${targetHeight}.jpg`
      );

      await resizeImg(inputPath, customOutputPath, targetWidth, targetHeight);

      const metadata = await sharp(customOutputPath).metadata();
      expect(metadata.width).toEqual(targetWidth);
      expect(metadata.height).toEqual(targetHeight);
    });

    it('should maintain aspect ratio when resizing to square dimensions', async (): Promise<void> => {
      const targetSize = 150;
      const squareOutputPath = path.join(
        cacheDir,
        `sample_test_${targetSize}x${targetSize}.jpg`
      );

      await resizeImg(inputPath, squareOutputPath, targetSize, targetSize);

      const exists = fs.existsSync(squareOutputPath);
      expect(exists).toBeTrue();

      const metadata = await sharp(squareOutputPath).metadata();
      expect(metadata.width).toEqual(targetSize);
      expect(metadata.height).toEqual(targetSize);
    });

    it('should throw an error for non-existent images', async (): Promise<void> => {
      const nonExistentPath = path.join(testImageDir, 'non-existent.jpg');

      await expectAsync(
        resizeImg(nonExistentPath, outputPath, 200, 200)
      ).toBeRejected();
    });

    it('should handle invalid dimensions gracefully', async (): Promise<void> => {
      await expectAsync(
        resizeImg(inputPath, outputPath, 0, 200)
      ).toBeRejected();

      await expectAsync(
        resizeImg(inputPath, outputPath, -100, 200)
      ).toBeRejected();
    });
  });

  describe('Image Metadata', (): void => {
    it('should get image metadata correctly', async (): Promise<void> => {
      const metadata = await getImgInfo(inputPath);
      expect(metadata).toBeDefined();
      expect(metadata.format).toBeDefined();
      expect(metadata.width).toBeGreaterThan(0);
      expect(metadata.height).toBeGreaterThan(0);
    });

    it('should return correct format for JPEG images', async (): Promise<void> => {
      const metadata = await getImgInfo(inputPath);
      expect(metadata.format).toBe('jpeg');
    });

    it('should return valid dimensions', async (): Promise<void> => {
      const metadata = await getImgInfo(inputPath);
      expect(typeof metadata.width).toBe('number');
      expect(typeof metadata.height).toBe('number');
      expect(metadata.width).toBeGreaterThan(0);
      expect(metadata.height).toBeGreaterThan(0);
    });

    it('should throw an error for non-existent image metadata', async (): Promise<void> => {
      const nonExistentPath = path.join(testImageDir, 'non-existent.jpg');

      await expectAsync(
        getImgInfo(nonExistentPath)
      ).toBeRejected();
    });
  });

  describe('File Operations', (): void => {
    it('should create output file in correct location', async (): Promise<void> => {
      const testOutputPath = path.join(cacheDir, 'test-output.jpg');

      await resizeImg(inputPath, testOutputPath, 100, 100);

      expect(fs.existsSync(testOutputPath)).toBeTrue();

      // Clean up
      if (fs.existsSync(testOutputPath)) {
        fs.unlinkSync(testOutputPath);
      }
    });

    it('should overwrite existing output file', async (): Promise<void> => {
      const testOutputPath = path.join(cacheDir, 'overwrite-test.jpg');

      // Create first version
      await resizeImg(inputPath, testOutputPath, 100, 100);
      const firstStats = fs.statSync(testOutputPath);

      // Wait a moment to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      // Create second version with different dimensions
      await resizeImg(inputPath, testOutputPath, 200, 200);
      const secondStats = fs.statSync(testOutputPath);

      expect(firstStats.mtime.getTime()).toBeLessThan(secondStats.mtime.getTime());

      // Verify new dimensions
      const metadata = await sharp(testOutputPath).metadata();
      expect(metadata.width).toBe(200);
      expect(metadata.height).toBe(200);

      // Clean up
      if (fs.existsSync(testOutputPath)) {
        fs.unlinkSync(testOutputPath);
      }
    });
  });
});
