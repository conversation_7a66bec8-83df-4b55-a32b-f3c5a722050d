import { resizeImg, getImgInfo } from '../controllers/processing';
import path from 'path';
import fs from 'fs';
import sharp from 'sharp';

// Test whole image's process
describe('Image Processing Functions', () => {
  const testImageDir = path.resolve('images');
  const cacheDir = path.resolve('cache');
  const testImageName = 'sample.jpg';
  const inputPath = path.join(testImageDir, testImageName);
  const outputPath = path.join(cacheDir, 'sample_test_200x200.jpg');

  afterEach((): void => {
    if (fs.existsSync(outputPath)) {
      fs.unlinkSync(outputPath);
    }
  });

  beforeAll((): void => {
    if (!fs.existsSync(testImageDir)) {
      fs.mkdirSync(testImageDir, { recursive: true });
    }
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }

    if (!fs.existsSync(inputPath)) {
      console.warn(`Test image ${inputPath} not found. Some tests may fail.`);
    }
  });

  it('should resize the image successfully', async (): Promise<void> => {
    if (!fs.existsSync(inputPath)) {
      pending('Test image not found');
      return;
    }

    await resizeImg(inputPath, outputPath, 200, 200);
    const exists = fs.existsSync(outputPath);
    expect(exists).toBeTrue();
  });

  it('should create an image with the correct dimensions', async (): Promise<void> => {
    if (!fs.existsSync(inputPath)) {
      pending('Test image not found');
      return;
    }

    const targetWidth = 300;
    const targetHeight = 200;
    const customOutputPath = path.join(
      cacheDir,
      `sample_test_${targetWidth}x${targetHeight}.jpg`
    );

    try {
      await resizeImg(inputPath, customOutputPath, targetWidth, targetHeight);

      const metadata = await sharp(customOutputPath).metadata();
      expect(metadata.width).toEqual(targetWidth);
      expect(metadata.height).toEqual(targetHeight);

      if (fs.existsSync(customOutputPath)) {
        fs.unlinkSync(customOutputPath);
      }
    } catch (error) {
      fail(
        `Test failed with error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  });

  it('should get image metadata correctly', async (): Promise<void> => {
    if (!fs.existsSync(inputPath)) {
      pending('Test image not found');
      return;
    }

    const metadata = await getImgInfo(inputPath);
    expect(metadata).toBeDefined();
    expect(metadata.format).toBeDefined();
    expect(metadata.width).toBeGreaterThan(0);
    expect(metadata.height).toBeGreaterThan(0);
  });

  it('should throw an error for non-existent images', async (): Promise<void> => {
    const nonExistentPath = path.join(testImageDir, 'non-existent.jpg');

    await expectAsync(
      resizeImg(nonExistentPath, outputPath, 200, 200)
    ).toBeRejected();
  });
});
