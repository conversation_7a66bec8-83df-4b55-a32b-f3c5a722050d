import supertest from 'supertest';
import app from '../index';
import fs from 'fs';
import path from 'path';

const request = supertest(app);

describe('Upload API Endpoints', (): void => {
  const testAssetsDir = path.resolve('tests/assets');
  const testImagePath = path.join(testAssetsDir, 'test.jpg');

  beforeAll((): void => {
    // Ensure test assets directory exists
    if (!fs.existsSync(testAssetsDir)) {
      fs.mkdirSync(testAssetsDir, { recursive: true });
    }

    // Verify test image exists
    if (!fs.existsSync(testImagePath)) {
      throw new Error(`Test image ${testImagePath} not found. Please ensure tests/assets/test.jpg exists.`);
    }
  });

  describe('POST /api/upload', (): void => {
    it('should upload JPG image successfully', async (): Promise<void> => {
      const res = await request
        .post('/api/upload')
        .attach('image', testImagePath);

      expect(res.status).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.message).toBe('Image uploaded successfully');
      expect(res.body.file).toBeDefined();
      expect(res.body.file.filename).toBeDefined();
      expect(res.body.file.originalname).toBeDefined();
      expect(res.body.file.size).toBeGreaterThan(0);
      expect(res.body.file.path).toBeDefined();
    });

    it('should reject request with no file', async (): Promise<void> => {
      const res = await request.post('/api/upload');

      expect(res.status).toBe(400);
      expect(res.body.success).toBe(false);
      expect(res.body.message).toBe('No file uploaded');
    });

    it('should reject unsupported file type', async (): Promise<void> => {
      // Create a temporary text file with PNG extension
      const tempPngPath = path.join(testAssetsDir, 'temp-test.png');
      fs.writeFileSync(tempPngPath, 'This is not an image file');

      const res = await request
        .post('/api/upload')
        .attach('image', tempPngPath);

      expect(res.status).toBe(400);
      expect(res.body.success).toBe(false);

      // Clean up
      if (fs.existsSync(tempPngPath)) {
        fs.unlinkSync(tempPngPath);
      }
    });

    it('should handle large file rejection', async (): Promise<void> => {
      // Create a large dummy file (over 5MB)
      const largePath = path.join(testAssetsDir, 'large-test.jpg');
      const largeBuffer = Buffer.alloc(6 * 1024 * 1024, 'a'); // 6MB
      fs.writeFileSync(largePath, largeBuffer);

      const res = await request
        .post('/api/upload')
        .attach('image', largePath);

      expect(res.status).toBe(400);
      expect(res.body.success).toBe(false);

      // Clean up
      if (fs.existsSync(largePath)) {
        fs.unlinkSync(largePath);
      }
    });
  });

  describe('GET /api/images', (): void => {
    it('should return list of available images', async (): Promise<void> => {
      const res = await request.get('/api/images');

      expect(res.status).toBe(200);
      expect(res.body.success).toBe(true);
      expect(res.body.count).toBeDefined();
      expect(res.body.images).toBeDefined();
      expect(Array.isArray(res.body.images)).toBe(true);
    });

    it('should return images with correct structure', async (): Promise<void> => {
      const res = await request.get('/api/images');

      expect(res.status).toBe(200);

      if (res.body.images.length > 0) {
        const firstImage = res.body.images[0];
        expect(firstImage.filename).toBeDefined();
        expect(firstImage.path).toBeDefined();
        expect(firstImage.url).toBeDefined();
        expect(firstImage.path).toMatch(/^\/images\//);
        expect(firstImage.url).toMatch(/^\/images\/resize\?filename=.*&width=300&height=200$/);
      }
    });
  });
});
