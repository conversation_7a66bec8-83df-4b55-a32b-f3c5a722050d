const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function createTestImage() {
  try {
    const testsAssetsDir = path.resolve('tests/assets');
    const imagesDir = path.resolve('images');
    
    if (!fs.existsSync(testsAssetsDir)) {
      fs.mkdirSync(testsAssetsDir, { recursive: true });
    }
    
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }

    const testImageBuffer = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    })
    .jpeg({ quality: 80 })
    .toBuffer();

    const testImagePath = path.join(testsAssetsDir, 'test.jpg');
    fs.writeFileSync(testImagePath, testImageBuffer);
    console.log(`Created test image: ${testImagePath}`);

    const sampleImagePath = path.join(imagesDir, 'sample.jpg');
    fs.writeFileSync(sampleImagePath, testImageBuffer);
    console.log(`Created sample image: ${sampleImagePath}`);

    console.log('Test images created successfully!');
  } catch (error) {
    console.error('Error creating test images:', error);
    process.exit(1);
  }
}

createTestImage();
