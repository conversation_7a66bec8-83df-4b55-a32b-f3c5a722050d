const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function createTestImg() {
  try {
    const testsAssetsDir = path.resolve('tests/assets');
    const ImgsDir = path.resolve('Imgs');
    
    if (!fs.existsSync(testsAssetsDir)) {
      fs.mkdirSync(testsAssetsDir, { recursive: true });
    }
    
    if (!fs.existsSync(ImgsDir)) {
      fs.mkdirSync(ImgsDir, { recursive: true });
    }

    const testImgBuffer = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    })
    .jpeg({ quality: 80 })
    .toBuffer();

    const testImgPath = path.join(testsAssetsDir, 'test.jpg');
    fs.writeFileSync(testImgPath, testImgBuffer);
    console.log(`Created test Img: ${testImgPath}`);

    const sampleImgPath = path.join(ImgsDir, 'sample.jpg');
    fs.writeFileSync(sampleImgPath, testImgBuffer);
    console.log(`Created sample Img: ${sampleImgPath}`);

    console.log('Test Imgs created successfully!');
  } catch (error) {
    console.error('Error creating test Imgs:', error);
    process.exit(1);
  }
}

createTestImg();
