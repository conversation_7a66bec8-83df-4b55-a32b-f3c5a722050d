import { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';

export const uploadImg = (req: Request, res: Response): void => {
  try {
    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'No file uploaded',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Image uploaded successfully',
      file: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        size: req.file.size,
        path: req.file.path,
      },
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).json({
      success: false,
      message: `Error uploading image: ${errorMessage}`,
    });
  }
};

export const getImg = (req: Request, res: Response): void => {
  try {
    const imagesDir = path.resolve('images');

    if (!fs.existsSync(imagesDir)) {
      res.status(404).json({
        success: false,
        message: 'Images directory not found',
      });
      return;
    }

    const files = fs
      .readdirSync(imagesDir)
      .filter((file) => {
        const ext = path.extname(file).toLowerCase();
        return ext === '.jpg' || ext === '.jpeg';
      })
      .map((file) => ({
        filename: file,
        path: `/images/${file}`,
        url: `/images/resize?filename=${file}&width=300&height=200`,
      }));

    res.status(200).json({
      success: true,
      count: files.length,
      images: files,
    });
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    res.status(500).json({
      success: false,
      message: `Error getting images: ${errorMessage}`,
    });
  }
};
