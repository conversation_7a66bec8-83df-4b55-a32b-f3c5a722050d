import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request, Response, NextFunction } from 'express';

// Ensure the upload directory exists
const uploadDir = path.resolve('images');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (
    _req: Request,
    _file: Express.Multer.File,
    cb: (error: Error | null, destination: string) => void
  ): void => {
    cb(null, uploadDir);
  },
  filename: (
    _req: Request,
    file: Express.Multer.File,
    cb: (error: Error | null, filename: string) => void
  ): void => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname).toLowerCase();
    cb(null, path.basename(file.originalname, ext) + '-' + uniqueSuffix + ext);
  },
});

// File filter to only allow jpg/jpeg files
const fileFilter = (
  _req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
): void => {
  const ext = path.extname(file.originalname).toLowerCase();
  if (ext === '.jpg' || ext === '.jpeg') {
    cb(null, true);
  } else {
    cb(null, false);
  }
};

// Create the multer upload instance
export const uploadConfig = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
});

// Custom middleware to handle file type validation errors
export const upload = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const singleUpload = uploadConfig.single('image');

  singleUpload(req, res, (err: unknown): void => {
    if (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Error uploading file';
      res.status(400).json({
        success: false,
        message: errorMessage,
      });
      return;
    }

    if (!req.file && req.body && req.body.image) {
      res.status(400).json({
        success: false,
        message: 'Only JPG/JPEG files are allowed',
      });
      return;
    }

    next();
  });
};
