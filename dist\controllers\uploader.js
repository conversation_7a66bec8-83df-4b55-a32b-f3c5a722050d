"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getImage = exports.uploadImage = void 0;
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const uploadImage = (req, res) => {
    try {
        if (!req.file) {
            res.status(400).json({
                success: false,
                message: 'No file uploaded',
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Image uploaded successfully',
            file: {
                filename: req.file.filename,
                originalname: req.file.originalname,
                size: req.file.size,
                path: req.file.path,
            },
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        res.status(500).json({
            success: false,
            message: `Error uploading Image: ${errorMessage}`,
        });
    }
};
exports.uploadImage = uploadImage;
const getImage = (req, res) => {
    try {
        const ImagesDir = path_1.default.resolve('Images');
        if (!fs_1.default.existsSync(ImagesDir)) {
            res.status(404).json({
                success: false,
                message: 'Images directory not found',
            });
            return;
        }
        const files = fs_1.default
            .readdirSync(ImagesDir)
            .filter((file) => {
            const ext = path_1.default.extname(file).toLowerCase();
            return ext === '.jpg' || ext === '.jpeg';
        })
            .map((file) => ({
            filename: file,
            path: `/Images/${file}`,
            url: `/Images/resize?filename=${file}&width=300&height=200`,
        }));
        res.status(200).json({
            success: true,
            count: files.length,
            Images: files,
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        res.status(500).json({
            success: false,
            message: `Error getting Images: ${errorMessage}`,
        });
    }
};
exports.getImage = getImage;
