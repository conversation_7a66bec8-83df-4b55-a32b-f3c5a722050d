{"name": "eslint-plugin-html", "version": "8.1.3", "description": "A ESLint plugin to lint and fix inline scripts contained in HTML files.", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/BenoitZugmeyer/eslint-plugin-html"}, "homepage": "https://github.com/BenoitZugmeyer/eslint-plugin-html", "bugs": "https://github.com/BenoitZugmeyer/eslint-plugin-html/issues", "keywords": ["eslint-plugin", "eslintplugin", "eslint", "html"], "main": "src/index.js", "engines": {"node": ">=16.0.0"}, "dependencies": {"htmlparser2": "^10.0.0"}, "devDependencies": {"@html-eslint/eslint-plugin": "^0.40.0", "@html-eslint/parser": "^0.40.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.0", "markdown-link-check": "^3.10.2", "prettier": "^3.2.4", "semver": "^7.3.2"}, "scripts": {"validate": "npm run lint && npm run test", "test": "node --test", "lint": "eslint .", "format": "prettier --check ."}}