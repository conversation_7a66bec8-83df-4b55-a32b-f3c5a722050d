"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.upload = exports.uploadConfig = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
// Upload directory path (created in index.ts)
const uploadDir = path_1.default.resolve('Images');
// Configure storage
const storage = multer_1.default.diskStorage({
    destination: (_req, _file, cb) => {
        cb(null, uploadDir);
    },
    filename: (_req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
        const ext = path_1.default.extname(file.originalname).toLowerCase();
        cb(null, path_1.default.basename(file.originalname, ext) + '-' + uniqueSuffix + ext);
    },
});
// File filter to only allow jpg/jpeg files
const fileFilter = (_req, file, cb) => {
    const ext = path_1.default.extname(file.originalname).toLowerCase();
    if (ext === '.jpg' || ext === '.jpeg') {
        cb(null, true);
    }
    else {
        cb(null, false);
    }
};
// Create the multer upload instance
exports.uploadConfig = (0, multer_1.default)({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB max file size
    },
});
// Custom middleware to handle file type validation errors
const upload = (req, res, next) => {
    const singleUpload = exports.uploadConfig.single('Image');
    singleUpload(req, res, (err) => {
        if (err) {
            const errorMessage = err instanceof Error ? err.message : 'Error uploading file';
            res.status(400).json({
                success: false,
                message: errorMessage,
            });
            return;
        }
        if (!req.file && req.body && req.body.Image) {
            res.status(400).json({
                success: false,
                message: 'Only JPG/JPEG files are allowed',
            });
            return;
        }
        next();
    });
};
exports.upload = upload;
