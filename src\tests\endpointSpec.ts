import supertest from 'supertest';
import app from '../index';
import fs from 'fs';
import path from 'path';

const request = supertest(app);

describe('API Endpoints', (): void => {
  // Test the API home endpoint
  describe('Test /api endpoint', (): void => {
    it('should return welcome message', async (): Promise<void> => {
      const res = await request.get('/api');
      expect(res.status).toBe(200);
      expect(res.text).toContain('Welcome to the Image API');
    });
  });

  // Test the image resize endpoint
  describe('Test /images/resize endpoint', (): void => {
    const testImageDir = path.resolve('images');
    const testImagePath = path.join(testImageDir, 'sample.jpg');
    const testAssetsDir = path.resolve('tests/assets');
    const testAssetPath = path.join(testAssetsDir, 'test.jpg');

    beforeAll((): void => {
      // Ensure test directories exist
      if (!fs.existsSync(testImageDir)) {
        fs.mkdirSync(testImageDir, { recursive: true });
      }

      // Copy test image from assets if it doesn't exist
      if (!fs.existsSync(testImagePath) && fs.existsSync(testAssetPath)) {
        fs.copyFileSync(testAssetPath, testImagePath);
      }

      // Verify test image exists
      if (!fs.existsSync(testImagePath)) {
        throw new Error(
          `Test image ${testImagePath} not found. Please ensure tests/assets/test.jpg exists.`
        );
      }
    });

    it('should return status 200 for a valid query', async (): Promise<void> => {
      const res = await request.get(
        '/images/resize?filename=sample.jpg&width=200&height=200'
      );
      expect(res.status).toBe(200);
      expect(res.headers['content-type']).toMatch(/image/);
    });

    it('should return status 400 for missing query parameters', async (): Promise<void> => {
      const res = await request.get('/images/resize?filename=sample.jpg');
      expect(res.status).toBe(400);
    });

    it('should return status 400 for invalid dimensions', async (): Promise<void> => {
      const res = await request.get(
        '/images/resize?filename=sample.jpg&width=abc&height=200'
      );
      expect(res.status).toBe(400);
    });

    it('should return status 404 for non-existent images', async (): Promise<void> => {
      const res = await request.get(
        '/images/resize?filename=nonexistent.jpg&width=200&height=200'
      );
      expect(res.status).toBe(404);
    });

    it('should return status 400 for missing filename parameter', async (): Promise<void> => {
      const res = await request.get('/images/resize?width=200&height=200');
      expect(res.status).toBe(400);
    });

    it('should return status 400 for negative dimensions', async (): Promise<void> => {
      const res = await request.get(
        '/images/resize?filename=sample.jpg&width=-100&height=200'
      );
      expect(res.status).toBe(400);
    });
  });
});
