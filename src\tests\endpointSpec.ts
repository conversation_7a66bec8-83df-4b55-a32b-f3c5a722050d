import supertest from 'supertest';
import app from '../index';
import fs from 'fs';
import path from 'path';

const request = supertest(app);

describe('API Endpoints', () => {
  // Test the API home endpoint
  describe('Test /api endpoint', () => {
    it('should return welcome message', async () => {
      const res = await request.get('/api');
      expect(res.status).toBe(200);
      expect(res.text).toContain('Welcome to the Image API');
    });
  });

  // Test the image resize endpoint
  describe('Test /images/resize endpoint', () => {
    beforeAll(() => {
      const testImageDir = path.resolve('images');
      const testImagePath = path.join(testImageDir, 'sample.jpg');

      if (!fs.existsSync(testImageDir)) {
        fs.mkdirSync(testImageDir, { recursive: true });
      }

      if (!fs.existsSync(testImagePath)) {
        console.warn(
          `Test image ${testImagePath} not found. Some tests may fail.`
        );
      }
    });

    it('should return status 200 for a valid query', async () => {
      const res = await request.get(
        '/images/resize?filename=sample.jpg&width=200&height=200'
      );
      expect(res.status).toBe(200);
    });

    it('should return status 400 for missing query parameters', async () => {
      const res = await request.get('/images/resize?filename=sample.jpg');
      expect(res.status).toBe(400);
    });

    it('should return status 400 for invalid dimensions', async () => {
      const res = await request.get(
        '/images/resize?filename=sample.jpg&width=abc&height=200'
      );
      expect(res.status).toBe(400);
    });

    it('should return status 404 for non-existent images', async () => {
      const res = await request.get(
        '/images/resize?filename=nonexistent.jpg&width=200&height=200'
      );
      expect(res.status).toBe(404);
    });
  });
});
