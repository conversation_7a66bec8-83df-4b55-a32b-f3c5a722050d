"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getImageInfo = exports.resizeImage = void 0;
const sharp_1 = __importDefault(require("sharp"));
const path_1 = __importDefault(require("path"));
const resizeImage = (inputPath, outputPath, width, height) => __awaiter(void 0, void 0, void 0, function* () {
    // Validate input parameters
    if (!inputPath || !outputPath) {
        throw new Error('Input and output paths are required');
    }
    if (!width || !height || width <= 0 || height <= 0) {
        throw new Error('Width and height must be positive numbers');
    }
    try {
        const ext = path_1.default.extname(inputPath).toLowerCase();
        const useSharp = (0, sharp_1.default)(inputPath).resize(width, height);
        if (ext === '.jpg' || ext === '.jpeg') {
            yield useSharp.jpeg({ quality: 80 }).toFile(outputPath);
        }
        else if (ext === '.png') {
            yield useSharp.png().toFile(outputPath);
        }
        else {
            yield useSharp.jpeg({ quality: 80 }).toFile(outputPath);
        }
    }
    catch (error) {
        if (error instanceof Error) {
            throw new Error(`Error resizing Image: ${error.message}`);
        }
        else {
            throw new Error('Unknown error occurred while resizing Image');
        }
    }
});
exports.resizeImage = resizeImage;
const getImageInfo = (ImagePath) => __awaiter(void 0, void 0, void 0, function* () {
    // Validate input parameter
    if (!ImagePath) {
        throw new Error('Image path is required');
    }
    try {
        return yield (0, sharp_1.default)(ImagePath).metadata();
    }
    catch (error) {
        if (error instanceof Error) {
            throw new Error(`Error getting Image info: ${error.message}`);
        }
        else {
            throw new Error('Unknown error occurred while getting Image info');
        }
    }
});
exports.getImageInfo = getImageInfo;
