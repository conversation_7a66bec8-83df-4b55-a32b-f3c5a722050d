import sharp from 'sharp';
import path from 'path';

export const resizeImage = async (
  inputPath: string,
  outputPath: string,
  width: number,
  height: number
): Promise<string | void> => {
  // Validate input parameters
  if (!inputPath || !outputPath) {
    throw new Error('Input and output paths are required');
  }

  if (!width || !height || width <= 0 || height <= 0) {
    throw new Error('Width and height must be positive numbers');
  }

  try {
    const ext = path.extname(inputPath).toLowerCase();
    const useSharp = sharp(inputPath).resize(width, height);

    if (ext === '.jpg' || ext === '.jpeg') {
      await useSharp.jpeg({ quality: 80 }).toFile(outputPath);
    } else if (ext === '.png') {
      await useSharp.png().toFile(outputPath);
    } else {
      await useSharp.jpeg({ quality: 80 }).toFile(outputPath);
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Error resizing Image: ${error.message}`);
    } else {
      throw new Error('Unknown error occurred while resizing Image');
    }
  }
};

export const getImageInfo = async (
  ImagePath: string
): Promise<sharp.Metadata> => {
  // Validate input parameter
  if (!ImagePath) {
    throw new Error('Image path is required');
  }

  try {
    return await sharp(ImagePath).metadata();
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Error getting Image info: ${error.message}`);
    } else {
      throw new Error('Unknown error occurred while getting Image info');
    }
  }
};
