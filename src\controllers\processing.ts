import sharp from 'sharp';
import path from 'path';

export const resizeImg = async (
  inputPath: string,
  outputPath: string,
  width: number,
  height: number
): Promise<string | void> => {
  try {
    const ext = path.extname(inputPath).toLowerCase();
    const useSharp = sharp(inputPath).resize(width, height);

    if (ext === '.jpg' || ext === '.jpeg') {
      await useSharp.jpeg({ quality: 80 }).toFile(outputPath);
    } else if (ext === '.png') {
      await useSharp.png().toFile(outputPath);
    } else {
      await useSharp.jpeg({ quality: 80 }).toFile(outputPath);
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Error resizing image: ${error.message}`);
    } else {
      throw new Error('Unknown error occurred while resizing image');
    }
  }
};

export const getImgInfo = async (
  imagePath: string
): Promise<sharp.Metadata> => {
  try {
    return await sharp(imagePath).metadata();
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Error getting image info: ${error.message}`);
    } else {
      throw new Error('Unknown error occurred while getting image info');
    }
  }
};
