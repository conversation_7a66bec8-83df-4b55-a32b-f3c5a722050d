"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const api_1 = __importDefault(require("./routes/api"));
const imgRoute_1 = __importDefault(require("./routes/imgRoute"));
const upRoute_1 = __importDefault(require("./routes/upRoute"));
const app = (0, express_1.default)();
const port = 3000;
// Ensure required directories exist
const requiredDirs = ['images', 'cache', 'public'];
requiredDirs.forEach((dir) => {
    const dirPath = path_1.default.resolve(dir);
    if (!fs_1.default.existsSync(dirPath)) {
        fs_1.default.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
    }
});
// middleware
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
app.use(express_1.default.static(path_1.default.join(__dirname, '../public')));
app.use('/images', express_1.default.static(path_1.default.join(__dirname, '../images')));
//routes
app.use('/api', api_1.default);
app.use('/images', imgRoute_1.default);
app.use('/api', upRoute_1.default);
//start app
app.listen(port, () => {
    console.log(`Server is working at port ${port}`);
});
exports.default = app;
